package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.MathQuestionReviewType;
import com.joinus.knowledge.model.entity.MathQuestionReviewRecords;
import com.joinus.knowledge.model.vo.MathQuestionReviewRecordVO;
import com.joinus.knowledge.model.vo.MathQuestionReviewUserStatisticsVO;

import java.util.Date;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_review_records(数学题审核记录表)】的数据库操作Service
* @createDate 2025-06-16 16:25:44
*/
public interface MathQuestionReviewRecordsService extends IService<MathQuestionReviewRecords> {
    int claimQuestions(int count, String username, MathQuestionReviewType reviewType);

    Page<MathQuestionReviewRecordVO> listReviewRecordsByUser(Page<MathQuestionReviewRecordVO> pageParam, String username, UUID questionId, String status, MathQuestionReviewType reviewType, Date startDate, Date endDate);

    void verify(UUID questionId, boolean verified, String remark, MathQuestionReviewType reviewType, String userName);

    MathQuestionReviewUserStatisticsVO getUserStatistics(String username, MathQuestionReviewType reviewType);

    void sendBackReview(UUID questionId);

}
