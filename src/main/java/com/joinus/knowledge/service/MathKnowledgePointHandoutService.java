package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.param.AddMathHandoutParam;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_handouts(讲义表)】的数据库操作Service
* @createDate 2025-08-19 13:49:20
*/
public interface MathKnowledgePointHandoutService extends IService<MathKnowledgePointHandout> {

    void batchAdd(List<AddMathHandoutParam> params);
}
