package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.joinus.knowledge.model.vo.*;
import jakarta.validation.Valid;

import java.util.*;

/**
* <AUTHOR>
* @description 针对表【math_questions】的数据库操作Service
* @createDate 2025-02-28 14:12:06
*/
public interface MathQuestionsService extends IService<MathQuestion> {

    List<QuestionDetailVO> getKeypointDetail(QueryKeyPointDetailParam param);

    List<QuestionDetailVO> updateKeypointDetail(UpdateQuestionDetailParam param);

    String encodeContent(String content);

    String decodeContentV2(String content);

    QuestionDetailVO getDetailById(UUID uuid);

    List<QuestionDetailVO> listDetailByIds(List<UUID> ids);

    void deleteKeyPoints(DeleteKeypointParam param);

    TextbookPointVO createKeypointByPageNo(CreateKeypointParam param);

    Page<MathQuestionVO> page(Page<MathQuestionVO> pageParam, PageQuestionParam pageQuestionParam);

    List<MathAnswerVO> listAnswersByQuestionId(UUID id);

    List<ExamQuestionPO> getQuestionIdsByKnowledgePoints(List<UUID> knowledgePoints);

    List<QuestionWithLatestAnswerVO> listNoKnowledgeDomainLabelQuestions(Integer count);

    List<QuestionWithLatestAnswerVO> listMathQuestionByKnowledgeDomain(String labelName, Integer count);

    Page<MathQuestionGraphicsScriptVO> queryQuestionGraphicsScriptByUser(Page<MathQuestionGraphicsScriptVO> pageParam, String username, String labelName, UUID questionId, String status, Date startDate, Date endDate);

    void updateEnabled(UpdateQuestionEnabledParam param);

    QuestionDetailVO getParentBookQuestion(UUID id);

    QuestionDetailVO updateById(UUID id, UpdateQuestionParam param);

    List<MathQuestion> listMultiKnowledgePointQuestionFromBook();

    List<MathQuestion> listMultiQuestionTypesMappingFromBook();

    KnowledgePointAndQuestionTypeVO listKnowledgePointsAndQuestionTypes(UUID id);

    Map<UUID, KnowledgePointAndQuestionTypeVO> listKnowledgePointsAndQuestionTypesByIds(List<UUID> ids);

    Map<String, Object> listMathSpecialTrainingQuestionsV2(MathTrainingQuestionsParamV2 param);

    Map<String, Object> listQuestionsByExamIdForTraining(UUID examId, ExamSourceType source);

    List<QuestionDetailVO> getDerivativeAiBookQuestion(UUID id);

    List<QuestionDetailVO> list(String content, QuestionSourceType source);

    void bindQuestionWithKeyPoint(UUID id, BindQuestionKeyPointParam param);

    void unbindQuestionWithKeyPoint(UUID id, BindQuestionKeyPointParam param);

    List<QuestionDetailVO> listExamQuestionDetailByExamId(UUID examId);

    List<MathQuestionDimensionVO> listMathQuestionDimensions(UUID id);

    void deleteAnswer(UUID id, UUID answerId);

    List<MathQuestion> listNoneKnowledgePointsExamQuestions();

    List<MathQuestion> listAvailableAIQuestionsByKnowledgePointId(UUID knowledgePointId);

    List<MathQuestion> listAvailableAIQuestionsByQuestionTypeId(UUID questionTypeId);

    Map<String, Object> listMathHolidayTrainingQuestionsBySectionId(@Valid MathTrainingQuestionsParamV2 param);

    HashMap<String, Object> listMathHolidayTrainingQuestionsByChapterId(@Valid MathTrainingQuestionsParamV2 param);

    /*
     * 章末测试，题目按照正常试卷排序（选择、填空、解答）
     */
    @Deprecated
    List<MathQuestionVO> listMathHolidayTrainingQuestionsByChapterIdV0(@Valid MathTrainingQuestionsParamV2 param);

    List<MathQuestionVO> listMathHolidayTrainingQuestions(@Valid MathTrainingQuestionsParamV2 param);

    List<MathQuestionVO> listQuestionsByExamIdForTraining(UUID examId);


    Boolean checkSolved(MathQuestion question);

    Boolean checkRelatedKnowledgePoints(MathQuestion question, PublisherType publisher);

    void changeQuestionKeyPoint(UUID id, BindQuestionKeyPointParam param);
}
