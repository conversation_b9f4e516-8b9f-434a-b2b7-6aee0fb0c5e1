package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathHandoutSlideshowPresentation;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_handout_slideshow_presentations(讲义幻灯片展示表)】的数据库操作Service
* @createDate 2025-08-19 13:49:20
*/
public interface MathHandoutSlideshowPresentationService extends IService<MathHandoutSlideshowPresentation> {

    void saveOrUpdate(UUID id, List<MathHandoutSlideParam> slides);
}
