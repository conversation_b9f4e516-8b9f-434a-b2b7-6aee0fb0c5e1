package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.KnowledgePointHandoutFileType;
import com.joinus.knowledge.mapper.MathKnowledgePointHandoutFileMapper;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandoutFile;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathKnowledgePointHandoutFileService;
import jakarta.annotation.Resource;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_handout_files(知识点讲义文件表)】的数据库操作Service实现
* @createDate 2025-08-19 13:49:20
*/
@Service
public class MathKnowledgePointHandoutFileServiceImpl extends ServiceImpl<MathKnowledgePointHandoutFileMapper, MathKnowledgePointHandoutFile>
    implements MathKnowledgePointHandoutFileService{

    @Resource
    private FilesService filesService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveKnowledgePointHandoutFileAndRelation(UUID id, List<UploadFileParam> uploadFiles) {
        if (CollUtil.isEmpty(uploadFiles)) {
            return;
        }

        List<MathKnowledgePointHandoutFile> list = new ArrayList<>();
        uploadFiles.forEach(uploadFile -> {
            String objectName = uploadFile.getOssKey();
            String originalType = null;
            String originalMimeType = null;
            KnowledgePointHandoutFileType type = null;
            if (objectName.endsWith(".ppt") || objectName.endsWith(".pptx")) {
                originalType = objectName.endsWith(".ppt") ? "ppt" : "pptx";
                type = KnowledgePointHandoutFileType.PPT;
            } else if (objectName.endsWith(".mp4")) {
                originalType = "mp4";
                type = KnowledgePointHandoutFileType.MP4;
            } else if (objectName.endsWith(".pdf")) {
                originalType = "pdf";
                type = KnowledgePointHandoutFileType.PDF;
            }else {
                throw new IllegalArgumentException("不支持的格式");
            }
            originalMimeType = MediaTypeFactory.getMediaType(objectName).orElseThrow().toString();

            File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalType, originalMimeType, uploadFile.getOssKey(), uploadFile.getOssEnum());
            MathKnowledgePointHandoutFile mathKnowledgePointHandoutFile = MathKnowledgePointHandoutFile.builder()
                    .id(UUID.randomUUID())
                    .handoutId(id)
                    .fileId(file.getId())
                    .type(type)
                    .build();
            list.add(mathKnowledgePointHandoutFile);
        });
        remove(lambdaQuery()
                .eq(MathKnowledgePointHandoutFile::getHandoutId, id)
                .eq(MathKnowledgePointHandoutFile::getType, list.getFirst().getType()).getWrapper());
        saveBatch(list);
    }
}




