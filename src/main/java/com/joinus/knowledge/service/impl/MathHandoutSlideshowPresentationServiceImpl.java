package com.joinus.knowledge.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.MathHandoutSlideshowPresentationMapper;
import com.joinus.knowledge.model.entity.MathHandoutSlideshowPresentation;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;
import com.joinus.knowledge.service.MathHandoutSlideshowPresentationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_handout_slideshow_presentations(讲义幻灯片展示表)】的数据库操作Service实现
* @createDate 2025-08-19 13:49:20
*/
@Slf4j
@Service
public class MathHandoutSlideshowPresentationServiceImpl extends ServiceImpl<MathHandoutSlideshowPresentationMapper, MathHandoutSlideshowPresentation>
    implements MathHandoutSlideshowPresentationService{

    @Override
    public void saveOrUpdate(UUID id, List<MathHandoutSlideParam> slides) {
        MathHandoutSlideshowPresentation existSlideshow = lambdaQuery().eq(MathHandoutSlideshowPresentation::getHandoutId, id)
                .one();
        if (null == existSlideshow) {
            existSlideshow = MathHandoutSlideshowPresentation.builder()
                    .handoutId(id)
                    .pptHtmls(JSONUtil.toJsonStr(slides))
                    .build();
            save(existSlideshow);
        }
        MathHandoutSlideshowPresentation updateSlideShow = MathHandoutSlideshowPresentation.builder()
                .id(existSlideshow.getId())
                .pptHtmls(JSONUtil.toJsonStr(slides))
                .build();
        updateById(updateSlideShow);
    }
}




