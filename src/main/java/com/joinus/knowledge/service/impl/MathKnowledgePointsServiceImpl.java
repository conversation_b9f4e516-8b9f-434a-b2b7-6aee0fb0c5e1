package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.KnowledgePointFileCategory;
import com.joinus.knowledge.enums.KnowledgePointHandoutFileType;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.MathKnowledgePointsMapper;
import com.joinus.knowledge.model.dto.MediaData;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.MathKnowledgePointParam;
import com.joinus.knowledge.model.param.PageKnowledgePointParam;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.param.SectionKnowledgePointParam;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import com.joinus.knowledge.model.vo.SectionKnowledgePointVO;
import com.joinus.knowledge.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_points】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Service
public class MathKnowledgePointsServiceImpl extends ServiceImpl<MathKnowledgePointsMapper, MathKnowledgePoint>
    implements MathKnowledgePointsService{

    @Autowired
    private MathCatalogNodesService mathCatalogNodesService;
    @Autowired
    private SectionKnowledgePointsService sectionKnowledgePointsService;
    @Autowired
    private MathQuestionTypesService questionTypesService;
    @Lazy
    @Autowired
    private MathQuestionsService mathQuestionsService;
    @Autowired
    private MathKnowledgePointHandoutService mathKnowledgePointHandoutService;
    @Autowired
    private MathKnowledgePointFilesService mathKnowledgePointFilesService;
    @Autowired
    private FilesService filesService;
    @Autowired
    private MathKnowledgePointHandoutFileService mathKnowledgePointHandoutFileService;


    @Override
    public MathKnowledgePoint save(Integer pageNo, String name, UUID textbookId, Integer sortNo, boolean isExamPoint) {
        List<MathCatalogNodes> sections = mathCatalogNodesService.getByPageNo(pageNo, textbookId, null);
        if (CollUtil.isEmpty(sections)) {
            throw new RuntimeException("小节不存在，不能添加知识点。");
        }
        MathCatalogNodes section = sections.get(0);

        MathKnowledgePoint mathKnowledgePoint = MathKnowledgePoint.builder()
                .name(name)
                .sortNo(null == sortNo ? 1 : sortNo)
                .examPoint(isExamPoint)
                .build();
        baseMapper.insert(mathKnowledgePoint);
        sectionKnowledgePointsService.createRelation(section.getId(), mathKnowledgePoint.getId(), pageNo);
        return mathKnowledgePoint;

//        LambdaQueryWrapper<MathKnowledgePoint> wrapper = Wrappers.lambdaQuery(MathKnowledgePoint.class)
//                .eq(MathKnowledgePoint::getName, name)
//                .eq(MathKnowledgePoint::getExamPoint, isExamPoint);
//
//        List<MathKnowledgePoint> mathKnowledgePoints = baseMapper.selectList(wrapper);
//        if (CollUtil.isNotEmpty(mathKnowledgePoints)) {
//            sectionKnowledgePointsService.createRelation(section.getId(), mathKnowledgePoints.get(0).getId(), pageNo);
//            return mathKnowledgePoints.get(0);
//        } else {
//            MathKnowledgePoint mathKnowledgePoint = MathKnowledgePoint.builder()
//                    .name(name)
//                    .sortNo(1)
//                    .examPoint(isExamPoint)
//                    .build();
//            baseMapper.insert(mathKnowledgePoint);
//            sectionKnowledgePointsService.createRelation(section.getId(), mathKnowledgePoint.getId(), pageNo);
//            return mathKnowledgePoint;
//        }
    }

    @Override
    public List<MathKnowledgePoint> listByGradeAndSemester(Integer grade, Integer semester, PublisherType publisher) {
        return baseMapper.listByGradeAndSemester(grade,semester, publisher);
    }

    @Override
    public List<MathKnowledgePoint> listByQuestionId(UUID questionId) {
        return baseMapper.listByQuestionId(questionId);
    }

    @Override
    public List<MathKnowledgePoint> listByPublisher(String publisher) {
        return baseMapper.listByPublisher(publisher);
    }

    @Override
    public List<MathKnowledgePointVO> list(String name, Integer grade, Integer semester, PublisherType publisher, UUID chapterId, String chapterName, UUID sectionId, String sectionName) {
        return baseMapper.list(name, grade, semester, publisher, chapterId, chapterName, sectionId, sectionName);
    }

    @Override
    public List<MathKnowledgePoint> listByTextbookId(UUID textbookId) {
        return baseMapper.listByTextbookId(textbookId);
    }

    @Override
    public List<MathKnowledgePointPO> listByQuestionIds(List<UUID> ids) {
        return baseMapper.listByQuestionIds(ids);
    }

    @Override
    public List<MathKnowledgePointPO> listByQuestionIdsAndPublisher(List<UUID> ids, PublisherType publisher) {
        return baseMapper.listByQuestionIdsAndPublisher(ids, publisher);
    }


    @Override
    public List<MathKnowledgePointVO> listQuestionTypeByKnowledgePointIds(List<UUID> knowledgePoints) {
        return baseMapper.listQuestionTypeByKnowledgePointIds(knowledgePoints);
    }

    @Override
    public List<SectionKnowledgePointVO> listSectionKnowledgePointByKnowledgeIds(SectionKnowledgePointParam param) {
        return baseMapper.listSectionKnowledgePointByKnowledgeIds(param.getKnowledgePointIds(), param.getGrade(), param.getSemester(), param.getPublisher());
    }

    @Override
    public Map<String, Object> listQuestionTypeByKnowledgePointIdsV2(List<UUID> knowledgePointIds) {

        List<MathKnowledgePointVO> kpResults = new ArrayList<>();
        List<MathKnowledgePointVO> kpVOs = baseMapper.listEnableAiQuestionCountByKnowledgePointIds(knowledgePointIds);
        kpResults = kpVOs.stream().filter(kpVO -> kpVO.getEnableAiQuestionCount() > 0).toList();

        List<MathQuestionTypeVO> qtResults = new ArrayList<>();
        List<MathQuestionTypeVO> questionTypes =questionTypesService.listEnableAiQuestionCountByKnowledgePointIds(knowledgePointIds);
        qtResults = questionTypes.stream().filter(qtVO -> qtVO.getEnableAiQuestionCount() > 0).toList();

        HashMap<String, Object> result = new HashMap<String, Object>();
        result.put("knowledgePoints", kpResults);
        result.put("questionTypes", qtResults);

        return result;
    }

    @Override
    public List<MathKnowledgePointVO> listBySectionIds(List<UUID> sectionIds) {
        return baseMapper.listBySectionIds(sectionIds);
    }

    @Override
    public List<MathKnowledgePointVO> listByIdsAndPublisher(List<UUID> ids, PublisherType publisher, Integer grade, Integer semester) {
        return baseMapper.listByIdsAndPublisher(ids, publisher, grade, semester);
    }

    @Override
    public List<MathKnowledgePointVO> listByIds(List<UUID> ids) {
        return baseMapper.listByIds(ids);
    }

    @Override
    public List<MathKnowledgePointVO> listKnowledgePointByQuestionId(UUID questionId, Integer grade, Integer semester, PublisherType publisher) {
        return baseMapper.listKnowledgePointByQuestionId(questionId, grade, semester, publisher);
    }

    @Override
    public Page<MathKnowledgePointVO> page(Page<MathKnowledgePointVO> pageParam, PageKnowledgePointParam param) {
        Page<MathKnowledgePointVO> page = baseMapper.pageQuery(pageParam, param);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MathKnowledgePointParam knowledgePointParam) {
        if (knowledgePointParam.getHandout() != null) {
            knowledgePointParam.setHandout(mathQuestionsService.encodeContent(knowledgePointParam.getHandout()));
            Optional<MathKnowledgePointHandout> knowledgePointHandoutOpt = mathKnowledgePointHandoutService.lambdaQuery().eq(MathKnowledgePointHandout::getKnowledgePointId, knowledgePointParam.getId()).oneOpt();
            if (knowledgePointHandoutOpt.isPresent()) {
                knowledgePointHandoutOpt.get().setContentMarkdown(knowledgePointParam.getHandout());
                mathKnowledgePointHandoutService.updateById(knowledgePointHandoutOpt.get());
            } else {
                MathKnowledgePointHandout knowledgePointHandout = MathKnowledgePointHandout.builder()
                        .knowledgePointId(knowledgePointParam.getId())
                        .contentMarkdown(knowledgePointParam.getHandout())
                        .build();
                mathKnowledgePointHandoutService.save(knowledgePointHandout);
            }
        }
        if (knowledgePointParam.getContent() != null) {
            knowledgePointParam.setContent(mathQuestionsService.encodeContent(knowledgePointParam.getContent()));
        }
        UUID newCatalogNodeId = knowledgePointParam.getCatalogNodeId();
        if (newCatalogNodeId != null) {
            List<UUID> currentNodeIds = sectionKnowledgePointsService.getSectionIdsByKnowledgePointId(knowledgePointParam.getId());
            if (!currentNodeIds.contains(newCatalogNodeId)) {
                sectionKnowledgePointsService.deleteAssociationsByKnowledgePointId(knowledgePointParam.getId());
                sectionKnowledgePointsService.createRelation(newCatalogNodeId, knowledgePointParam.getId(), null);
            }
        }
        MathKnowledgePoint updateKnowledgePoint = getById(knowledgePointParam.getId());
        BeanUtil.copyProperties(knowledgePointParam, updateKnowledgePoint);
        return updateById(updateKnowledgePoint);
    }

    @Override
    public MathKnowledgePointVO getDetailById(UUID id) {
        Page<MathKnowledgePointVO> pageParam = new Page<>(1, 1);
        PageKnowledgePointParam pageKnowledgePointParam = new PageKnowledgePointParam();
        pageKnowledgePointParam.setId(id);
        Page<MathKnowledgePointVO> resultPage = page(pageParam, pageKnowledgePointParam);
        MathKnowledgePointVO knowledgePoint = resultPage.getRecords().getFirst();
        Assert.notNull(knowledgePoint, "知识点不存在");
        if (StrUtil.isNotBlank(knowledgePoint.getHandout())) {
            knowledgePoint.setHandout(mathQuestionsService.decodeContentV2(knowledgePoint.getHandout()));
        }
        if (StrUtil.isNotBlank(knowledgePoint.getContent())) {
            knowledgePoint.setContent(mathQuestionsService.decodeContentV2(knowledgePoint.getContent()));
        }
        List<MathKnowledgePointFiles> knowledgePointFiles = mathKnowledgePointFilesService.lambdaQuery()
                .eq(MathKnowledgePointFiles::getKnowledgePointId, id)
                .orderByAsc(MathKnowledgePointFiles::getSortNo)
                .list();

        if (CollUtil.isNotEmpty(knowledgePointFiles)) {
            Map<KnowledgePointFileCategory, List<MathKnowledgePointFiles>> map = knowledgePointFiles.stream().filter(file -> file.getCategory() == KnowledgePointFileCategory.IMAGE).collect(Collectors.groupingBy(MathKnowledgePointFiles::getCategory));
            Map<UUID, Integer> fileIdSortNoMap = knowledgePointFiles.stream().collect(Collectors.toMap(MathKnowledgePointFiles::getFileId, MathKnowledgePointFiles::getSortNo));
            if (map.containsKey(KnowledgePointFileCategory.IMAGE) && CollUtil.isNotEmpty(map.get(KnowledgePointFileCategory.IMAGE))) {
                List<File> files = filesService.listByIds(map.get(KnowledgePointFileCategory.IMAGE)
                        .stream()
                        .map(MathKnowledgePointFiles::getFileId)
                        .collect(Collectors.toList()));
                List<MediaData> images = files.stream()
                        .map(file -> {
                            OssEnum ossEnum = OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket());
                            return new MediaData(ossEnum, file.getOssUrl(), filesService.getOssUrl(file.getOssUrl(), ossEnum), fileIdSortNoMap.get(file.getId()));
                        })
                        .toList();
                knowledgePoint.setOriginalImageList(images);
            }
        }

        if (knowledgePoint.getHandoutId() != null) {
            List<MathKnowledgePointHandoutFile> handoutFiles = mathKnowledgePointHandoutFileService.lambdaQuery()
                    .eq(MathKnowledgePointHandoutFile::getHandoutId, knowledgePoint.getHandoutId())
                    .list();
            Map<KnowledgePointHandoutFileType, List<MathKnowledgePointHandoutFile>> handoutFileMap = handoutFiles.stream().collect(Collectors.groupingBy(MathKnowledgePointHandoutFile::getType));

            if (handoutFileMap.containsKey(KnowledgePointHandoutFileType.PPT) && CollUtil.isNotEmpty(handoutFileMap.get(KnowledgePointHandoutFileType.PPT))) {
                List<File> files = filesService.listByIds(handoutFileMap.get(KnowledgePointHandoutFileType.PPT)
                        .stream()
                        .map(MathKnowledgePointHandoutFile::getFileId)
                        .collect(Collectors.toList()));
                List<MediaData> ppt = files.stream()
                        .map(file -> {
                            OssEnum ossEnum = OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket());
                            return new MediaData(ossEnum, file.getOssUrl(), filesService.getOssUrl(file.getOssUrl(), ossEnum));
                        })
                        .toList();
                knowledgePoint.setPptList(ppt);
            }
            if (handoutFileMap.containsKey(KnowledgePointHandoutFileType.MP4) && CollUtil.isNotEmpty(handoutFileMap.get(KnowledgePointHandoutFileType.MP4))) {
                List<File> files = filesService.listByIds(handoutFileMap.get(KnowledgePointHandoutFileType.MP4)
                        .stream()
                        .map(MathKnowledgePointHandoutFile::getFileId)
                        .collect(Collectors.toList()));
                List<MediaData> video = files.stream()
                        .map(file -> {
                            OssEnum ossEnum = OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket());
                            return new MediaData(ossEnum, file.getOssUrl(), filesService.getOssUrl(file.getOssUrl(), ossEnum));
                        })
                        .toList();
                knowledgePoint.setVideoList(video);
            }
            if (handoutFileMap.containsKey(KnowledgePointHandoutFileType.PDF) && CollUtil.isNotEmpty(handoutFileMap.get(KnowledgePointHandoutFileType.PDF))) {
                List<File> files = filesService.listByIds(handoutFileMap.get(KnowledgePointHandoutFileType.PDF)
                        .stream()
                        .map(MathKnowledgePointHandoutFile::getFileId)
                        .collect(Collectors.toList()));
                List<MediaData> pdf = files.stream()
                        .map(file -> {
                            OssEnum ossEnum = OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket());
                            return new MediaData(ossEnum, file.getOssUrl(), filesService.getOssUrl(file.getOssUrl(), ossEnum));
                        })
                        .toList();
                knowledgePoint.setPdfList(pdf);
            }
        }
        return knowledgePoint;
    }

}
