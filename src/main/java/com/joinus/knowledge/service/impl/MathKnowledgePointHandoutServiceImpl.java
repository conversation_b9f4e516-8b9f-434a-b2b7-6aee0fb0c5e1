package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.MathKnowledgePointHandoutMapper;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.param.AddMathHandoutParam;
import com.joinus.knowledge.service.MathHandoutSlideshowPresentationFileService;
import com.joinus.knowledge.service.MathHandoutSlideshowPresentationService;
import com.joinus.knowledge.service.MathKnowledgePointHandoutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_handouts(讲义表)】的数据库操作Service实现
* @createDate 2025-08-19 13:49:20
*/
@Service
public class MathKnowledgePointHandoutServiceImpl extends ServiceImpl<MathKnowledgePointHandoutMapper, MathKnowledgePointHandout>
    implements MathKnowledgePointHandoutService{

    @Autowired
    private MathHandoutSlideshowPresentationService mathHandoutSlideshowPresentationService;
    @Autowired
    private MathHandoutSlideshowPresentationFileService mathHandoutSlideshowPresentationFileService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<AddMathHandoutParam> params) {
        params.forEach(handoutParam -> {
            MathKnowledgePointHandout handout = lambdaQuery().eq(MathKnowledgePointHandout::getKnowledgePointId, handoutParam.getKnowledgePointId())
                    .one();
            if (null == handout) {
                handout = MathKnowledgePointHandout.builder()
                        .knowledgePointId(handoutParam.getKnowledgePointId())
                        .contentMarkdown("")
                        .build();
                save(handout);
            }

            mathHandoutSlideshowPresentationService.saveOrUpdate(handout.getId(), handoutParam.getSlideshowPages());
            mathHandoutSlideshowPresentationFileService.saveOrUpdateAudioFiles(handout.getId(), handoutParam.getSlideshowPages());
        });
    }
}




