package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.KnowledgePointFileCategory;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathKnowledgePointFiles;
import com.joinus.knowledge.model.param.MathKnowledgePointParam;
import com.joinus.knowledge.model.param.PageKnowledgePointParam;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 数学知识点管理 Controller
 */
@RestController
@RequestMapping("/api/math/knowledge-points")
@RequiredArgsConstructor
public class MathKnowledgePointsController {

    private final MathKnowledgePointsService mathKnowledgePointsService;
    private final MathKnowledgePointFilesService mathKnowledgePointFilesService;
    private final SectionKnowledgePointsService sectionKnowledgePointsService;

    /**
     * 查询所有知识点
     * GET /api/math/knowledge-points
     */
    @GetMapping
    public Result<List<MathKnowledgePoint>> list() {
        return Result.success(mathKnowledgePointsService.list());
    }

    /**
     * 分页查询知识点
     * GET /api/math/knowledge-points/page?page=1&size=10
     */
    @GetMapping("/page")
    public Result<Page<MathKnowledgePoint>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name) {
        
        Page<MathKnowledgePoint> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<MathKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (name != null && !name.isEmpty()) {
            queryWrapper.like(MathKnowledgePoint::getName, name);
        }
        
        // 按排序号排序
        queryWrapper.orderByAsc(MathKnowledgePoint::getSortNo);
        
        Page<MathKnowledgePoint> resultPage = mathKnowledgePointsService.page(pageParam, queryWrapper);
        return Result.success(resultPage);
    }

    @PostMapping("/page")
    public Result<Page<MathKnowledgePointVO>> page(@RequestBody PageKnowledgePointParam pageKnowledgePointParam) {
        Page<MathKnowledgePointVO> pageParam = new Page<>(pageKnowledgePointParam.getPage(), pageKnowledgePointParam.getSize());
        Page<MathKnowledgePointVO> resultPage = mathKnowledgePointsService.page(pageParam, pageKnowledgePointParam);
        return Result.success(resultPage);
    }

    /**
     * 根据ID查询知识点
     * GET /api/math/knowledge-points/{id}
     */
    @GetMapping("/{id}")
    public Result<MathKnowledgePoint> getById(@PathVariable("id") UUID id) {
        MathKnowledgePoint knowledgePoint = mathKnowledgePointsService.getById(id);
        if (knowledgePoint == null) {
            return Result.error(404, "知识点不存在");
        }
        return Result.success(knowledgePoint);
    }

    @GetMapping("/detail/{id}")
    public Result<MathKnowledgePointVO> getDetailById(@PathVariable("id") UUID id) {
        MathKnowledgePointVO knowledgePoint = mathKnowledgePointsService.getDetailById(id);
        return Result.success(knowledgePoint);
    }

    /**
     * 创建知识点
     * POST /api/math/knowledge-points
     */
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Result<MathKnowledgePoint> create(@RequestBody MathKnowledgePointParam knowledgePoint) {

        // 生成UUID
        knowledgePoint.generateUUID();
        
        boolean success = mathKnowledgePointsService.save(knowledgePoint);

        if (knowledgePoint.getCatalogNodeId() != null) {
            sectionKnowledgePointsService.createRelation(knowledgePoint.getCatalogNodeId(), knowledgePoint.getId(), null);
        }

        if (success) {
            return Result.success(knowledgePoint);
        }
        return Result.error("创建知识点失败");
    }
    
    /**
     * 批量创建知识点
     * POST /api/math/knowledge-points/batch
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody List<MathKnowledgePoint> knowledgePointsList) {
        if (knowledgePointsList == null || knowledgePointsList.isEmpty()) {
            return Result.error("知识点列表不能为空");
        }
        
        // 过滤掉重复的知识点
        List<MathKnowledgePoint> pointsToSave = new ArrayList<>();
        
        for (MathKnowledgePoint point : knowledgePointsList) {
            // 检查是否存在相同的知识点（名称相同）
            LambdaQueryWrapper<MathKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MathKnowledgePoint::getName, point.getName());
            
            // 检查是否已存在
            MathKnowledgePoint existingPoint = mathKnowledgePointsService.getOne(queryWrapper);
            if (existingPoint == null) {
                // 只有不存在的知识点才添加到待保存列表
                pointsToSave.add(point);
            }
        }
        
        if (pointsToSave.isEmpty()) {
            // 所有知识点都已存在，无需添加
            return Result.success(true);
        }
        
        // 为每个知识点生成UUID
        pointsToSave.forEach(MathKnowledgePoint::generateUUID);
        
        // 批量保存知识点
        boolean success = mathKnowledgePointsService.saveBatch(pointsToSave);
        
        if (success) {
            return Result.success(true);
        }
        return Result.error("批量创建知识点失败");
    }
    
    /**
     * 更新知识点
     * PUT /api/math/knowledge-points/{id}
     */
    @PutMapping("/{id}")
    public Result<MathKnowledgePoint> update(@PathVariable("id") UUID id, @RequestBody MathKnowledgePointParam knowledgePoint) {
        // 确保要更新的ID正确
        knowledgePoint.setId(id);
        boolean success = mathKnowledgePointsService.update(knowledgePoint);
        if (success) {
            return Result.success(knowledgePoint);
        }
        return Result.error("更新知识点失败");
    }

    /**
     * 删除知识点
     * DELETE /api/math/knowledge-points/{id}
     */
    @DeleteMapping("/{id}")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(@PathVariable("id") UUID id) {
        boolean success = mathKnowledgePointsService.removeById(id);
        sectionKnowledgePointsService.deleteAssociationsByKnowledgePointId(id);
        if (success) {
            return Result.success(true);
        }
        return Result.error("删除知识点失败");
    }

    @PostMapping("/list")
    public Result<List<MathKnowledgePointVO>> list(@RequestBody List<UUID> kpIds) {
        return Result.success(mathKnowledgePointsService.listByIds(kpIds));
    }

    @PostMapping("/files/upload/{id}")
    public Result<Boolean> uploadFiles(@PathVariable("id") UUID id, @RequestBody List<UploadFileParam> uploadFiles) {
        mathKnowledgePointFilesService.saveKnowledgePointFileAndRelation(id, uploadFiles);
        return Result.success(true);
    }

    @DeleteMapping("/files/clear/{id}/{category}")
    public Result<Boolean> clearFiles(@PathVariable("id") UUID id,@PathVariable("category") KnowledgePointFileCategory category) {
        boolean result = mathKnowledgePointFilesService.remove(mathKnowledgePointFilesService.lambdaQuery().eq(MathKnowledgePointFiles::getKnowledgePointId, id).eq(MathKnowledgePointFiles::getCategory, category).getWrapper());
        return Result.success(result);
    }
}
