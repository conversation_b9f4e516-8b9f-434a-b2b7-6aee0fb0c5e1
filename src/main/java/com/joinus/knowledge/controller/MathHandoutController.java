package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.KnowledgePointHandoutFileType;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandoutFile;
import com.joinus.knowledge.model.param.AddMathHandoutParam;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.service.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 数学知识点管理 Controller
 */
@Tag(name = "数学-讲义相关", description = "数学-讲义相关")
@RestController
@RequestMapping("/api/math/handouts")
@RequiredArgsConstructor
public class MathHandoutController {

    private final MathKnowledgePointHandoutService handoutService;
    private final MathKnowledgePointHandoutFileService handoutFileService;

    @PostMapping("/batch")
    public Result<Boolean> batchAdd(@RequestBody List<AddMathHandoutParam> params) {
        handoutService.batchAdd(params);
        return Result.success();
    }

    @PostMapping("/review/{id}")
    public Result<Boolean> review(@PathVariable("id") UUID id, @RequestParam("reviewUser") String reviewUser) {
        boolean result = handoutService.lambdaUpdate()
                .set(MathKnowledgePointHandout::getReviewStatus, GlobalConstants.REVIEW_STATUS.APPROVED)
                .set(MathKnowledgePointHandout::getReviewUser, reviewUser)
                .set(MathKnowledgePointHandout::getReviewedAt, new Date())
                .eq(MathKnowledgePointHandout::getId, id)
                .update();
        return Result.success(result);
    }

    @PostMapping("/files/upload/{id}")
    public Result<Boolean> uploadFiles(@PathVariable("id") UUID id, @RequestBody List<UploadFileParam> uploadFiles) {
        handoutFileService.saveKnowledgePointHandoutFileAndRelation(id, uploadFiles);
        return Result.success(true);
    }

    @DeleteMapping("/files/clear/{id}/{type}")
    public Result<Boolean> clearFiles(@PathVariable("id") UUID id,@PathVariable("type") KnowledgePointHandoutFileType type) {
        boolean result = handoutFileService.remove(handoutFileService.lambdaQuery().eq(MathKnowledgePointHandoutFile::getHandoutId, id).eq(MathKnowledgePointHandoutFile::getType, type).getWrapper());
        return Result.success(result);
    }
}
