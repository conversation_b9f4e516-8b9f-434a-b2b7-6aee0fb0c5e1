package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.vo.OssFileVO;
import com.joinus.knowledge.service.OssService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/oss")
public class OssController {

    @Resource
    private OssService ossService;

    @GetMapping("/presigned-url")
    public Result<OssFileVO> getPresignedUrl(@RequestParam("ossEnum") OssEnum ossEnum,
                                             @RequestParam("ossKey") String ossKey) {
        OssFileVO ossFileVO = ossService.getPresignedInfo(ossEnum, ossKey);
        return Result.success(ossFileVO);
    }
}
