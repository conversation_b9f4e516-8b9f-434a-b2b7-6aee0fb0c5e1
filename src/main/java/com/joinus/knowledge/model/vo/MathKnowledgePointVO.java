package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.dto.MediaData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数学知识点列表")
public class MathKnowledgePointVO implements Serializable {

    @Schema(description = "知识点id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID id;

    @Schema(description = "知识点名称", implementation = String.class, example = "1")
    private String name;

    @Schema(description = "年级", implementation = Integer.class, example = "1")
    private Integer grade;

    @Schema(description = "学期", implementation = Integer.class, example = "1")
    private Integer semester;

    @Schema(description = "出版社", implementation = PublisherType.class, example = "1")
    private PublisherType publisher;

    @Schema(description = "出版社描述", implementation = String.class, example = "1")
    private String publisherDescription;

    @Schema(description = "章id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID chapterId;

    @Schema(description = "章名称", implementation = String.class, example = "1")
    private String chapterName;

    @Schema(description = "章排序号", implementation = Integer.class, example = "1")
    private Integer chapterSortNo;

    @Schema(description = "节id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID sectionId;

    @Schema(description = "节排序号", implementation = Integer.class, example = "1")
    private Integer sectionSortNo;

    @Schema(description = "节名称", implementation = String.class, example = "1")
    private String sectionName;

    @Schema(description = "页码", implementation = Integer.class, example = "1")
    private Integer pageIndex;

    @Schema(description = "上架的ai题目数量", implementation = Integer.class, example = "1")
    private Integer enableAiQuestionCount;

    @Schema(description = "教材id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID textbookId;

    @Schema(description = "知识点原名", implementation = String.class)
    private String originalName;

    @Schema(description = "知识点目录路径", implementation = String.class)
    private String fullPath;

    @Schema(description = "是否有内容", implementation = Boolean.class)
    private Boolean existContent;

    @Schema(description = "是否存在讲义", implementation = Boolean.class)
    private Boolean existHandout;

    @Schema(description = "是否存在图片", implementation = Boolean.class)
    private Boolean existImage;

    @Schema(description = "是否存在PPT", implementation = Boolean.class)
    private Boolean existPowerPoint;

    @Schema(description = "是否存在视频", implementation = Boolean.class)
    private Boolean existVideo;

    @Schema(description = "是否存在pdf", implementation = Boolean.class)
    private Boolean existPdf;

    @Schema(description = "讲义", implementation = Boolean.class)
    private String handout;

    @Schema(description = "知识点原图列表", implementation = List.class)
    private List<MediaData> originalImageList;

    @Schema(description = "知识点PPT列表", implementation = List.class)
    private List<MediaData> pptList;

    @Schema(description = "知识点视频列表", implementation = List.class)
    private List<MediaData> videoList;

    @Schema(description = "知识点PDF列表", implementation = List.class)
    private List<MediaData> pdfList;

    @Schema(description = "知识点内容，markdown格式", implementation = Boolean.class)
    private String content;

    @Schema(description = "标签列表", implementation = Boolean.class)
    private List<String> tags;

    @Schema(description = "审核状态", implementation = Boolean.class)
    private String reviewStatus;

    @Schema(description = "审核人", implementation = Boolean.class)
    private String reviewUser;

    @Schema(description = "目录id", implementation = Boolean.class)
    private UUID catalogNodeId;

    @Schema(description = "审核时间", implementation = Date.class)
    private Date reviewedAt;

    @Schema(description = "讲义id", implementation = UUID.class)
    private UUID handoutId;

    public String getPublisherDescription() {
        return null == publisher ? null : publisher.getValue();
    }



}
