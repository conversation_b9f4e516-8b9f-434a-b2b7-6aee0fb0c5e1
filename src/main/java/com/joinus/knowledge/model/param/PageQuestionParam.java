package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.MathQuestionReviewStatus;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.enums.QuestionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PageQuestionParam extends PageParam{

    private UUID id;
    private String content;
    private QuestionType questionType;
    private List<Integer> difficultys;
    private List<UUID> knowledgePointIds;
    private List<String> knowledgePointNames;
    private List<String> questionTypeNames;
    private QuestionSourceType source;
    private List<UUID> labelIds;
    private PublisherType publisher;
    private Integer grade;
    private Integer semester;
    private Boolean enabled;
    private UUID knowledgeDomainLabelId;
    private UUID catalogNodeId;
    private Integer validation;
    private Integer validationEight;
    /*
     * 标签，不同类型间是且，同类型间是或
     */
    private List<MathLabelParam> mathLabels;

    private List<String> notContainLabelTypes;

    private List<MathLabelParam> containLabels;

    private List<MathQuestionDimensionParam> mathQuestionDimensions;

    private List<MathQuestionDimensionParam> notContainDimensions;

    private List<MathQuestionDimensionParam> containDimensions;

    private List<MathQuestionReviewStatus> reviewStatusList;

}
