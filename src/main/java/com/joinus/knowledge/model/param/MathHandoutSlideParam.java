package com.joinus.knowledge.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathHandoutSlideParam implements Serializable {

    private Integer index;
    private String narration;
    private String html;
    private List<MathHandoutVoiceParam> voices;

}
