package com.joinus.knowledge.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrontendData implements Serializable {

    private List<WordData> words;
    private List<PhonemeData> phonemes;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WordData implements Serializable {
        private String word;
        private Double startTime;
        private Double endTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PhonemeData implements Serializable {
        private String phone;
        private Double startTime;
        private Double endTime;
    }

}
