<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathKnowledgePointsMapper">

    <resultMap id="knowledgePointAndQuestionTypes" type="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
    </resultMap>

    <select id="listByGradeAndSemester" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select vmkp.knowledge_point_id as id, vmkp.knowledge_point_name as name
        from view_math_knowledge_points vmkp
        where vmkp.exam_point = false
        <if test="grade != null and grade != ''">
            and vmkp.grade &lt;= #{grade}
        </if>
        <if test="publisher != null">
            and vmkp.publisher = #{publisher.value}
        </if>
    </select>
    <select id="listByQuestionId" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select mkp.* from math_knowledge_points mkp, math_knowledge_point_questions qkp
        where mkp.id = qkp.knowledge_point_id
          and qkp.question_id = #{questionId}
          and mkp.deleted_at is null
          and mkp.exam_point = false
    </select>
    <select id="listByPublisher" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select distinct mkp.* from math_knowledge_points mkp
            inner join math_knowledge_point_questions qkp on mkp.id = qkp.knowledge_point_id
            inner join view_math_knowledge_points vmkp on mkp.id = vmkp.knowledge_point_id
        where vmkp.publisher = #{publisher}
    </select>

    <select id="listQuestionTypeByKnowledgePointIds"
            resultMap="knowledgePointAndQuestionTypes">
        select mkp.id,
               mkp.name,
               mqt.id as question_type_id,
               mqt.name as question_type_name
        from
            math_knowledge_points mkp
                left join math_knowledge_point_question_types kqtr on mkp.id = kqtr.knowledge_point_id
                left join math_question_types mqt on mqt.id = kqtr.question_type_id and mqt.deleted_at is null
        where mkp.deleted_at is null
        and mkp.id in
        <foreach collection="knowledgePointIds" item="knowledgePointId" open="(" separator="," close=")">
            #{knowledgePointId}
        </foreach>
    </select>

    <select id="listSectionKnowledgePointByKnowledgeIds"
            resultType="com.joinus.knowledge.model.vo.SectionKnowledgePointVO">
        select vmkp.chapter_id,
        vmkp.chapter_name,
        vmkp.chapter_sort_no,
        vmkp.section_id,
        vmkp.section_name,
        vmkp.section_sort_no,
        vmkp.knowledge_point_id,
        vmkp.knowledge_point_name,
        vmkp.textbook_id,
        vmkp.grade,
        vmkp.semester,
        vmkp.publisher
        from view_math_knowledge_points vmkp
          where t.id in
        <foreach collection="knowledgePointIds" item="knowledgePointId" open="(" separator="," close=")">
            #{knowledgePointId}
        </foreach>
        <if test="grade != null">
            and vmkp.grade = #{grade}
        </if>
        <if test="semester != null">
            and vmkp.semester = #{semester}
        </if>
        <if test="publisher != null">
            and vmkp.publisher = #{publisher.value}
        </if>
    </select>

    <select id="list" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select distinct vmkp.knowledge_point_id as id,
        vmkp.knowledge_point_name as name,
        vmkp.grade,
        vmkp.semester,
        vmkp.publisher
        from view_math_knowledge_points vmkp
        <where>
            <if test="name != null and name != ''">
                and vmkp.knowledge_point_name like concat('%', #{name}, '%')
            </if>
            <if test="grade != null">
                and vmkp.grade = #{grade}
            </if>
            <if test="semester != null">
                and vmkp.semester = #{semester}
            </if>
            <if test="publisher != null">
              and vmkp.publisher = #{publisher}
            </if>
            <if test="chapterId != null">
                and vmkp.chapter_id = #{chapterId}
            </if>
            <if test="chapterName != null and chapterName != ''">
                and vmkp.chapter_name = #{chapterName}
            </if>
            <if test="sectionId != null">
                and vmkp.section_id = #{sectionId}
            </if>
            <if test="sectionName != null and sectionName != ''">
                and vmkp.section_name = #{sectionName}
            </if>
        </where>
    </select>

    <select id="listByTextbookId" resultType="com.joinus.knowledge.model.entity.MathKnowledgePoint">
        select mkp.* from math_knowledge_points mkp,view_math_knowledge_points vmkp
        where mkp.id = vmkp.knowledge_point_id
          and vmkp.textbook_id = #{textbookId}
    </select>

    <select id="listByQuestionIds" resultType="com.joinus.knowledge.model.po.MathKnowledgePointPO">
        select t.knowledge_point_id as id,
               vmkp.knowledge_point_name as name,
               vmkp.grade,
               vmkp.semester,
               vmkp.publisher,
               vmkp.chapter_id,
               vmkp.chapter_name,
               vmkp.chapter_sort_no,
               vmkp.section_id,
               vmkp.section_name,
               vmkp.section_sort_no,
               vmkp.exam_point,
               t.question_id,
               vmkp.full_path,
               vmkp.section_id as catalog_node_id
        from math_knowledge_point_questions t
            inner join view_math_knowledge_points vmkp on t.knowledge_point_id = vmkp.knowledge_point_id
            inner join math_knowledge_points mkp on t.knowledge_point_id = mkp.id
        where t.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </select>

    <select id="listByQuestionIdsAndPublisher" resultType="com.joinus.knowledge.model.po.MathKnowledgePointPO">
        select t.knowledge_point_id as id,
        vmkp.knowledge_point_name as name,
        vmkp.grade,
        vmkp.semester,
        vmkp.publisher,
        vmkp.chapter_id,
        vmkp.chapter_name,
        vmkp.chapter_sort_no,
        vmkp.section_id,
        vmkp.section_name,
        vmkp.section_sort_no,
        t.question_id
        from math_knowledge_point_questions t
        inner join view_math_knowledge_points vmkp on t.knowledge_point_id = vmkp.knowledge_point_id
        where t.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        <if test="null != publisher">
            and vmkp.publisher = #{publisher.value}
        </if>
    </select>

    <select id="listBySectionIds" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select vmkp.knowledge_point_id as id,
        vmkp.knowledge_point_name as name,
        vmkp.textbook_id,
        vmkp.publisher,
        vmkp.grade,
        vmkp.semester,
        vmkp.chapter_id,
        vmkp.chapter_name,
        vmkp.chapter_sort_no,
        vmkp.section_id,
        vmkp.section_name,
        vmkp.section_sort_no,
        skp.page_index
        from view_math_knowledge_points vmkp
        inner join math_section_knowledge_points skp on vmkp.knowledge_point_id = skp.knowledge_point_id
        where exam_point = false
        and vmkp.section_id in
        <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
            #{sectionId}
        </foreach>
        order by vmkp.grade, vmkp.semester, vmkp.chapter_sort_no, vmkp.section_sort_no, skp.page_index
    </select>

    <select id="listByIds" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select vmkp.knowledge_point_id as id,
        vmkp.knowledge_point_name as name,
        vmkp.textbook_id,
        vmkp.publisher,
        vmkp.grade,
        vmkp.semester,
        vmkp.chapter_id,
        vmkp.chapter_name,
        vmkp.chapter_sort_no,
        vmkp.section_id,
        vmkp.section_name,
        vmkp.section_sort_no,
        skp.page_index
        from view_math_knowledge_points vmkp
        inner join math_section_knowledge_points skp on vmkp.knowledge_point_id = skp.knowledge_point_id
        where vmkp.knowledge_point_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by vmkp.grade, vmkp.semester, vmkp.chapter_sort_no, vmkp.section_sort_no, skp.page_index
    </select>

    <select id="listKnowledgePointByQuestionId"
            resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select mkp.*,
            vmkp.grade,
            vmkp.semester,
            vmkp.publisher
               from math_questions t
                              inner join math_knowledge_point_questions kpq on t.id = kpq.question_id
                              inner join math_knowledge_points mkp on kpq.knowledge_point_id = mkp.id
                              inner join view_math_knowledge_points vmkp on mkp.id = vmkp.knowledge_point_id
        where t.deleted_at is null
          and t.id = #{questionId}
          <if test="grade != null">
            and vmkp.grade = #{grade}
          </if>
          <if test="semester != null">
              and vmkp.semester = #{semester}
          </if>
          <if test="publisher != null">
              and vmkp.publisher = #{publisher.value}
          </if>
        union
        select mkp.*,
            vmkp.grade,
            vmkp.semester,
            vmkp.publisher
        from math_questions t
                 inner join math_question_type_questions  qtq on t.id = qtq.question_id
                 inner join math_knowledge_point_question_types kpqt on qtq.question_type_id = kpqt.question_type_id
                 inner join math_knowledge_points mkp on kpqt.knowledge_point_id = mkp.id and mkp.deleted_at is null
                 inner join view_math_knowledge_points vmkp on mkp.id  = vmkp.knowledge_point_id
        where t.deleted_at is null
          and t.id = #{questionId}
            <if test="grade != null">
                and vmkp.grade = #{grade}
            </if>
            <if test="semester != null">
                and vmkp.semester = #{semester}
            </if>
            <if test="publisher != null">
                and vmkp.publisher = #{publisher.value}
            </if>
    </select>

    <select id="listByIdsAndPublisher" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select vmkp.knowledge_point_id as id,
        vmkp.knowledge_point_name as name,
        vmkp.textbook_id,
        vmkp.publisher,
        vmkp.grade,
        vmkp.semester,
        vmkp.chapter_id,
        vmkp.chapter_name,
        vmkp.chapter_sort_no,
        vmkp.section_id,
        vmkp.section_name,
        vmkp.section_sort_no,
        skp.page_index
        from view_math_knowledge_points vmkp
        inner join math_section_knowledge_points skp on vmkp.knowledge_point_id = skp.knowledge_point_id
        where vmkp.knowledge_point_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="publisher != null">
            and vmkp.publisher = #{publisher.value}
        </if>
        <if test="grade != null">
            and vmkp.grade = #{grade}
        </if>
        <if test="semester != null">
            and vmkp.semester = #{semester}
        </if>
        order by vmkp.grade, vmkp.semester, vmkp.chapter_sort_no, vmkp.section_sort_no, skp.page_index
    </select>

    <select id="listEnableAiQuestionCountByKnowledgePointIds"
            resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select mkp.id,
               mkp.name,
               count(distinct mq.id) as enableAiQuestionCount
        from
            math_knowledge_points mkp
                left join math_knowledge_point_questions  kpq on mkp.id = kpq.knowledge_point_id
                left join math_questions mq on kpq.question_id = mq.id and mq.deleted_at is null and mq.enabled = true and mq.source = 'AI'
        where mkp.deleted_at is null
            and mkp.id in
        <foreach collection="knowledgePointIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by mkp.id, mkp.name
    </select>
    <select id="pageQuery" resultType="com.joinus.knowledge.model.vo.MathKnowledgePointVO">
        select vmkp.knowledge_point_id as id,
               vmkp.knowledge_point_name as name,
               vmkp.knowledge_point_original_name as original_name,
               vmkp.grade,
               vmkp.semester,
               vmkp.publisher,
               vmkp.textbook_id,
               vmkp.full_path,
               vmkp.handout,
               vmkp.tags,
               vmkp.content,
               vmkp.review_status,
               vmkp.review_user,
               vmkp.section_id as catalog_node_id,
               vmkp.reviewed_at,
               vmkp.handout_id,
               case when vmkp.content is not null and vmkp.content != '' then true else false end as exist_content,
               case when vmkp.handout is not null and vmkp.handout != '' then true else false end as exist_handout,
               case when exists(select 1 from math_knowledge_point_files mkpf
                where mkpf.knowledge_point_id = vmkp.knowledge_point_id
                and mkpf.category = 'image') then true else false end as exist_image,
               case when exists(select 1 from math_knowledge_point_handouts mkph
                inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
                where mkph.knowledge_point_id = vmkp.knowledge_point_id
                and mkphf.type = 'PPT') then true else false end as exist_power_point,
               case when exists(select 1 from math_knowledge_point_handouts mkph
                inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
                where mkph.knowledge_point_id = vmkp.knowledge_point_id
                and mkphf.type = 'MP4') then true else false end as exist_video,
                case when exists(select 1 from math_knowledge_point_handouts mkph
                inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
                where mkph.knowledge_point_id = vmkp.knowledge_point_id
                and mkphf.type = 'PDF') then true else false end as exist_pdf
        from view_math_knowledge_points vmkp
        where vmkp.exam_point = false
          <if test="param.id != null">
            and vmkp.knowledge_point_id = #{param.id}
          </if>
          <if test="param.textbookId != null">
            and vmkp.textbook_id = #{param.textbookId}
          </if>
          <if test="param.publisher != null">
            and vmkp.publisher = #{param.publisher.value}
          </if>
          <if test="param.grade != null">
            and vmkp.grade = #{param.grade}
          </if>
          <if test="param.semester != null">
            and vmkp.semester = #{param.semester}
          </if>
          <if test="param.catalogNodeId != null">
            and vmkp.section_id in (SELECT id
              FROM math_catalog_nodes
              WHERE id_path &lt;@ (
              SELECT id_path
              FROM math_catalog_nodes
              WHERE id = #{param.catalogNodeId})
              AND deleted_at IS NULL)
          </if>
          <if test="param.name != null and param.name != ''">
            and vmkp.knowledge_point_name like '%' || #{param.name} || '%'
          </if>
          <if test="param.originalName != null and param.originalName != ''">
            and vmkp.knowledge_point_original_name like '%' || #{param.originalName} || '%'
          </if>
          <if test="param.existImage != null and param.existImage == true">
            and exists(select 1 from math_knowledge_point_files mkpf
              where mkpf.knowledge_point_id = vmkp.knowledge_point_id
              and mkpf.category = 'image')
          </if>
          <if test="param.existImage != null and param.existImage == false">
            and not exists(select 1 from math_knowledge_point_files mkpf
            where mkpf.knowledge_point_id = vmkp.knowledge_point_id
            and mkpf.category = 'image')
          </if>
        <if test="param.existPowerPoint != null and param.existPowerPoint == true">
            and exists(select 1 from math_knowledge_point_handouts mkph
            inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
            where mkph.knowledge_point_id = vmkp.knowledge_point_id
            and mkphf.type = 'PPT')
        </if>
        <if test="param.existPowerPoint != null and param.existPowerPoint == false">
            and not exists(select 1 from math_knowledge_point_handouts mkph
            inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
            where mkph.knowledge_point_id = vmkp.knowledge_point_id
            and mkphf.type = 'PPT')
        </if>
        <if test="param.existVideo != null and param.existVideo == true">
            and exists(select 1 from math_knowledge_point_handouts mkph
            inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
            where mkph.knowledge_point_id = vmkp.knowledge_point_id
            and mkphf.type = 'MP4')
        </if>
        <if test="param.existVideo != null and param.existVideo == false">
            and not exists(select 1 from math_knowledge_point_handouts mkph
            inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
            where mkph.knowledge_point_id = vmkp.knowledge_point_id
            and mkphf.type = 'MP4')
        </if>
        <if test="param.existPdf != null and param.existPdf == true">
            and exists(select 1 from math_knowledge_point_handouts mkph
            inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
            where mkph.knowledge_point_id = vmkp.knowledge_point_id
            and mkphf.type = 'PDF')
        </if>
        <if test="param.existPdf != null and param.existPdf == false">
            and not exists(select 1 from math_knowledge_point_handouts mkph
            inner join math_knowledge_point_handout_files mkphf on mkph.id = mkphf.handout_id
            where mkph.knowledge_point_id = vmkp.knowledge_point_id
            and mkphf.type = 'PDF')
        </if>
          <if test="param.existHandout != null and param.existHandout == true">
              and (vmkp.handout is not null and vmkp.handout != '')
          </if>
          <if test="param.existHandout != null and param.existHandout == false">
              and (vmkp.handout is null or vmkp.handout = '')
          </if>
        <if test="param.existContent != null and param.existContent == true">
            and (vmkp.content is not null and vmkp.content != '')
        </if>
        <if test="param.existContent != null and param.existContent == false">
            and (vmkp.content is null or vmkp.content = '')
        </if>
        <if test="param.tags != null and !param.tags.isEmpty() ">
            and vmkp.tags @> #{param.tags, jdbcType=ARRAY, typeHandler=com.joinus.knowledge.config.typehandler.StringListTypeHandler}
        </if>
        <if test="param.reviewed != null and param.reviewed == true">
            and vmkp.review_status = 'APPROVED'
        </if>
        <if test="param.reviewed != null and param.reviewed == false">
            and vmkp.review_status is null
        </if>
        order by vmkp.publisher, vmkp.grade, vmkp.semester, vmkp.chapter_sort_no, vmkp.section_sort_no, vmkp.sort_no
    </select>


</mapper>
