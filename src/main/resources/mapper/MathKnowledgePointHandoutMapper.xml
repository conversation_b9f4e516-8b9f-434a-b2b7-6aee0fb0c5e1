<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathKnowledgePointHandoutMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathKnowledgePointHandout">
            <result property="id" column="id" />
            <result property="knowledgePointId" column="knowledge_point_id" />
            <result property="contentMarkdown" column="content_markdown" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,knowledge_point_id,content_markdown,created_at,updated_at,deleted_at
    </sql>
</mapper>
