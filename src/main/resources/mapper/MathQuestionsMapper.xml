<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathQuestionsMapper">

    <!-- 为QuestionDetailVO定义resultMap -->
    <resultMap id="questionDetailWithAnswerResultMap" type="com.joinus.knowledge.model.vo.QuestionDetailVO">
        <id property="id" column="question_id"/>
        <result property="content" column="question_content"/>
        <result property="difficulty" column="difficulty"/>
        <result property="questionType" column="question_type"/>
        <result property="source" column="source"/>
        <collection property="answers" ofType="com.joinus.knowledge.model.vo.QuestionAnswerDetailVO">
            <id property="id" column="answer_id"/>
            <result property="answer" column="answer"/>
            <result property="content" column="answer_content"/>
        </collection>
        <collection property="files" ofType="com.joinus.knowledge.model.vo.FileVO">
            <id property="id" column="file_id"/>
            <result property="ossUrl" column="oss_url"/>
        </collection>
    </resultMap>

    <!-- 为ExamQuestionPO定义resultMap -->
    <resultMap id="examQuestionResultMap" type="com.joinus.knowledge.model.po.ExamQuestionPO">
        <id property="questionId" column="question_id"/>
        <result property="content" column="content"/>
        <result property="questionType" column="question_type"/>
        <result property="difficulty" column="difficulty"/>
        <result property="answerId" column="answer_id"/>
        <result property="answer" column="answer"/>
        <result property="answerContent" column="answer_content"/>
        <result property="ossKey" column="oss_key"/>
        <result property="ossType" column="oss_type"/>
        <result property="ossBucket" column="oss_bucket"/>
        <collection property="files" ofType="com.joinus.knowledge.model.entity.File">
            <id property="id" column="file_id"/>
            <result property="ossUrl" column="oss_key"/>
            <result property="ossType" column="oss_type"/>
            <result property="ossBucket" column="oss_bucket"/>
        </collection>
    </resultMap>

    <!-- 为MathQuestionVO定义resultMap -->
    <resultMap id="mathQuestionVOResultMap" type="com.joinus.knowledge.model.vo.MathQuestionVO">
        <id property="id" column="question_id"/>
        <result property="content" column="content"/>
        <result property="questionType" column="question_type"/>
        <result property="difficulty" column="difficulty"/>
        <result property="source" column="source"/>
        <result property="existGraphics" column="exist_graphics"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="enabled" column="enabled"/>
        <result property="reviewStatus" column="review_status"/>
        <result property="validationSuccess" column="validation_success"/>
        <result property="validationSuccessEight" column="validation_success_eight"/>
    </resultMap>

    <resultMap id="questionDetailResultMap" type="com.joinus.knowledge.model.vo.QuestionDetailVO">
        <id property="id" column="question_id"/>
        <result property="content" column="question_content"/>
        <result property="difficulty" column="difficulty"/>
        <result property="questionType" column="question_type"/>
        <result property="source" column="source"/>
        <result property="enabled" column="enabled"/>
        <result property="validationSuccess" column="validation_success"/>
        <result property="validationSuccessEight" column="validation_success_eight"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="reviewStatus" column="review_status"/>
        <collection property="files" ofType="com.joinus.knowledge.model.vo.FileVO">
            <id property="id" column="file_id"/>
            <result property="name" column="name"/>
            <result property="ossKey" column="oss_url"/>
            <result property="ossType" column="oss_type"/>
            <result property="ossBucket" column="oss_bucket"/>
            <result property="sortNo" column="sort_no"/>
            <result property="type" column="type"/>
        </collection>
    </resultMap>

    <!-- 根据问题类型查询问题详情 -->
    <select id="listQuestionAndAnswerByQuestionType"
            resultMap="questionDetailWithAnswerResultMap">
        select t.id as question_id,
               t.content as question_content,
               t.difficulty,
               t.question_type,
               t.source,
               mqa.id as answer_id,
               mqa.answer,
               mqa.content as answer_content,
               f.id as file_id,
               f.oss_url
        from math_questions t
                 inner join math_question_type_questions qtm on t.id = qtm.question_id
                 inner join math_question_types mqt on qtm.question_type_id = mqt.id
                 left join math_question_answers qar on t.id = qar.question_id
                 left join math_answers mqa on qar.answer_id = mqa.id and mqa.deleted_at is null
                 left join math_question_files qf on t.id = qf.question_id
                 left join files f on qf.file_id = f.id
        where t.deleted_at is null and mqt.deleted_at is null
          and mqt.id = #{keyPointId}
          and t.source = 'BOOK'
          order by t.updated_at asc
    </select>

    <!-- 根据知识点、考点查询问题详情 -->
    <select id="listQuestionAndAnswerByKnowledgePoint"
            resultMap="questionDetailWithAnswerResultMap">
        select t.id as question_id,
               t.content as question_content,
               t.difficulty,
               t.question_type,
               t.source,
               mqa.id as answer_id,
               mqa.answer,
               mqa.content as answer_content,
               f.id as file_id,
               f.oss_url
        from math_questions t
                 inner join math_knowledge_point_questions qkp on t.id = qkp.question_id
                 inner join math_knowledge_points mkp on qkp.knowledge_point_id = mkp.id
                 left join math_question_answers qar on t.id = qar.question_id
                 left join math_answers mqa on qar.answer_id = mqa.id and mqa.deleted_at is null
                 left join math_question_files qf on t.id = qf.question_id
                 left join files f on qf.file_id = f.id
        where t.deleted_at is null and mkp.deleted_at is null
          and mkp.id = #{keyPointId}
          and t.source = 'BOOK'
          order by t.updated_at asc
    </select>

    <select id="getDetailById" resultMap="questionDetailResultMap">
        select t.id as question_id,
               t.content as question_content,
               t.difficulty,
               t.question_type,
               t.source,
               t.enabled,
               qf.file_id as file_id,
               f.name,
               f.oss_url,
               f.oss_type,
               f.oss_bucket,
               t.created_at,
               t.updated_at,
               t.review_status,
               qf.sort_no,
               qf.type,
               mqd.validation_success,
               mqd.validation_success_eight
        from math_questions t
                 left join math_question_files qf on t.id = qf.question_id
                 left join files f on qf.file_id = f.id
                 left join math_question_dimensions mqd on t.id = mqd.question_id
        where t.deleted_at is null and t.id = #{uuid}
    </select>

    <select id="listQuestionByIds" resultMap="examQuestionResultMap">
        SELECT
            q.id AS question_id,
            q.content,
            q.question_type,
            q.difficulty,
            mqa.id AS answer_id,
            mqa.answer,
            mqa.content AS answer_content,
            f.id AS file_id,
            f.oss_url AS oss_key,
            f.oss_type,
            f.oss_bucket
        FROM math_questions q
        LEFT JOIN math_question_answers qar ON q.id = qar.question_id
        LEFT JOIN math_answers mqa ON qar.answer_id = mqa.id AND mqa.deleted_at IS NULL
        LEFT JOIN math_question_files qf ON q.id = qf.question_id
        LEFT JOIN files f ON qf.file_id = f.id
        WHERE q.deleted_at IS NULL 
        AND q.id IN 
        <foreach collection="questionIdList" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        ORDER BY q.created_at DESC
    </select>
    <select id="listTrainingQuestionIdsByKnowledgePoints" resultType="com.joinus.knowledge.model.entity.QuestionKnowledgePoint">
        WITH
            input_kps AS (
            SELECT unnest(ARRAY[
            <foreach collection="knowledgePointIds" item="knowledgePointId" separator=",">
                #{knowledgePointId}::uuid  <!-- 关键点：类型转换 -->
            </foreach>
            ]::uuid[]) AS kp_id  -- 使用命名参数接收数组(示例：'{1,2,3}'::int[])
            ),
        ranked_data AS (
            SELECT
                qkp.knowledge_point_id,
                qkp.question_id,
                CASE
                    WHEN q.review_status = 'APPROVED_SECOND_REVIEW' THEN 2
                    WHEN q.review_status = 'APPROVED_FIRST_REVIEW' THEN 1
                    ELSE 0
                    END AS sortNo,
                ROW_NUMBER() OVER (
                    PARTITION BY qkp.knowledge_point_id
                    ORDER BY
                        CASE
                            WHEN q.review_status = 'APPROVED_SECOND_REVIEW' THEN 2
                            WHEN q.review_status = 'APPROVED_FIRST_REVIEW' THEN 1
                            ELSE 0
                            END DESC,
                    random()
                    ) AS rk
            FROM math_knowledge_point_questions qkp
            INNER JOIN input_kps ik ON qkp.knowledge_point_id = ik.kp_id
            INNER JOIN math_questions q ON q.id = qkp.question_id AND q.source = 'AI' AND q.enabled = true
            )
        SELECT
            question_id,
            knowledge_point_id,
            sortNo
        FROM ranked_data
        WHERE rk &lt;= #{questionCount}
    </select>
    <select id="listNoKnowledgeDomainLabelQuestions"
            resultType="com.joinus.knowledge.model.vo.QuestionWithLatestAnswerVO">
        SELECT mq.id                 "questionId",
               mq.content            "questionContent",
               latest_answer.answer  "answer",
               latest_answer.content "answerContent"
        FROM math_questions mq
                 LEFT JOIN (SELECT DISTINCT ON (qar.question_id) qar.question_id,
            mqa.*
                            FROM math_question_answers qar
                                JOIN math_answers mqa
                            ON qar.answer_id = mqa.id
                            ORDER BY qar.question_id, mqa.created_at DESC) AS latest_answer
                           ON mq.id = latest_answer.question_id
        WHERE length(mq.content) > 5
          AND mq.source = 'BOOK'
          AND NOT EXISTS (SELECT 1
                  FROM math_question_labels ql
                  WHERE ql.question_id = mq.id
                    AND ql.label_type = 'KNOWLEDGE_DOMAIN')
        order by mq.created_at
            limit #{count}
    </select>
    <select id="listMathQuestionByKnowledgeDomain"
            resultType="com.joinus.knowledge.model.vo.QuestionWithLatestAnswerVO">
        SELECT mq.id                 "questionId",
        mq.content            "questionContent",
        mq.question_type      "questionType",
        latest_answer.answer  "answer",
        latest_answer.content "answerContent",
        COALESCE(mqr_count.derived_count, 0) "derivedCount"
        FROM math_questions mq
        LEFT JOIN (
        SELECT DISTINCT ON (qar.question_id) qar.question_id,
        mqa.answer,
        mqa.content,
        mqa.created_at
        FROM math_question_answers qar
        JOIN math_answers mqa ON qar.answer_id = mqa.id
        ORDER BY qar.question_id, mqa.created_at DESC
        ) AS latest_answer ON mq.id = latest_answer.question_id
        LEFT JOIN (
        SELECT mqr.base_question_id, COUNT(mqr.id) AS derived_count
        FROM math_question_relationships mqr
        GROUP BY mqr.base_question_id
        ) AS mqr_count ON mq.id = mqr_count.base_question_id
        WHERE length(mq.content) > 5
        AND mq.source = 'BOOK'
        AND EXISTS (
        SELECT 1
        FROM math_question_labels ql
        JOIN math_labels ml ON ql.label_id = ml.id
        WHERE ql.question_id = mq.id
        AND ml.type = 'KNOWLEDGE_DOMAIN'
        AND ml.name = #{labelName}
        )
        AND COALESCE(mqr_count.derived_count, 0) &lt; 10
        ORDER BY mq.created_at
        LIMIT #{count}
    </select>

    <select id="pageWithCustomQueryV3" resultMap="mathQuestionVOResultMap">
        select
        t.id as question_id,
        t.content,
        t.question_type,
        t.difficulty,
        t.source,
        t.exist_graphics,
        t.created_at,
        t.updated_at,
        t.enabled,
        t.review_status,
        a.validation_success,
        a.validation_success_eight
        from math_questions t
        inner join (
        SELECT distinct mq.id, mqd.validation_success,mqd.validation_success_eight
        FROM
        math_questions mq
        LEFT JOIN math_question_labels ql on mq.id = ql.question_id
        LEFT JOIN math_knowledge_point_questions qkp ON qkp.question_id = mq.id
        left join view_math_knowledge_points vmkp on qkp.knowledge_point_id = vmkp.knowledge_point_id
        left join math_question_type_questions qtm on qtm.question_id = mq.id
        left join view_math_question_types vmqt on qtm.question_type_id = vmqt.question_type_id
        left join math_question_dimensions mqd on mq.id = mqd.question_id
        where mq.deleted_at IS NULL
            <if test="param.id != null">
                AND mq.id = #{param.id}
            </if>
            <if test="param.content != null and param.content != ''">
                AND mq.content LIKE CONCAT('%', #{param.content}, '%')
            </if>
            <if test="param.questionType != null">
                AND mq.question_type = #{param.questionType}
            </if>
            <if test="param.difficultys != null and param.difficultys.size() > 0">
                AND mq.difficulty in
                <foreach collection="param.difficultys" item="difficulty" open="(" separator="," close=")">
                    #{difficulty}
                </foreach>
            </if>
            <if test="param.knowledgePointIds != null and param.knowledgePointIds.size() > 0">
                AND qkp.knowledge_point_id IN
                <foreach collection="param.knowledgePointIds" item="pointId" open="(" separator="," close=")">
                    #{pointId}
                </foreach>
            </if>
            <if test="param.knowledgePointNames != null and param.knowledgePointNames.size() > 0">
                AND vmkp.knowledge_point_name IN
                <foreach collection="param.knowledgePointNames" item="knowledgePointName" open="(" separator="," close=")">
                    #{knowledgePointName}
                </foreach>
            </if>
            <if test="param.questionTypeNames != null and param.questionTypeNames.size() > 0">
                AND vmqt.question_type_name IN
                <foreach collection="param.questionTypeNames" item="questionTypeName" open="(" separator="," close=")">
                    #{questionTypeName}
                </foreach>
            </if>
            <if test="param.source != null">
                AND mq.source = #{param.source}
            </if>
            <if test="param.labelIds != null and param.labelIds.size() > 0">
                <bind name="labelIdsSize" value="param.labelIds.size()" />
                AND mq.id IN (
                SELECT question_id
                FROM math_question_labels
                WHERE label_id IN
                <foreach collection="param.labelIds" item="labelId" open="(" separator="," close=")">
                    #{labelId}
                </foreach>
                GROUP BY question_id
                HAVING COUNT(DISTINCT label_id) = #{labelIdsSize}
                )
            </if>
            <if test="param.publisher != null">
                AND (vmkp.publisher = #{param.publisher.value,jdbcType=VARCHAR} or vmqt.publisher = #{param.publisher.value,jdbcType=VARCHAR})
            </if>
            <if test="param.grade != null">
                AND (vmkp.grade = #{param.grade} or vmqt.grade = #{param.grade})
            </if>
            <if test="param.semester != null">
                AND (vmkp.semester = #{param.semester} or vmqt.semester = #{param.semester})
            </if>
            <if test="param.catalogNodeId != null">
                and (vmkp.section_id in (SELECT id
                FROM math_catalog_nodes
                WHERE id_path &lt;@ (
                SELECT id_path
                FROM math_catalog_nodes
                WHERE id = #{param.catalogNodeId})
                AND deleted_at IS NULL) or
                vmqt.section_id in (SELECT id
                FROM math_catalog_nodes
                WHERE id_path &lt;@ (
                SELECT id_path
                FROM math_catalog_nodes
                WHERE id = #{param.catalogNodeId})
                AND deleted_at IS NULL))
            </if>
            <if test="param.enabled != null">
                AND mq.enabled = #{param.enabled}
            </if>
            <if test="param.reviewStatusList != null and param.reviewStatusList.size() > 0">
                AND mq.review_status in
                <foreach collection="param.reviewStatusList" item="reviewStatus" open="(" separator="," close=")">
                    #{reviewStatus}
                </foreach>
            </if>
            <if test="param.knowledgeDomainLabelId != null">
                and ql.label_id = #{param.knowledgeDomainLabelId}
            </if>
            <if test="param.validation != null">
                <choose>
                    <when test="param.validation == 1">
                        AND mqd.validation_success = true
                    </when>
                    <when test="param.validation == 2">
                        AND mqd.validation_success = false
                    </when>
                    <otherwise>
                        AND (mqd.question_id is null or mqd.validation_success IS NULL)
                    </otherwise>
                </choose>
            </if>
        <if test="param.validationEight != null">
            <choose>
                <when test="param.validationEight == 1">
                    AND mqd.validation_success_eight = true
                </when>
                <when test="param.validationEight == 2">
                    AND mqd.validation_success_eight = false
                </when>
                <otherwise>
                    AND (mqd.question_id is null or mqd.validation_success_eight IS NULL)
                </otherwise>
            </choose>
        </if>
        <if test="param.notContainLabelTypes != null and param.notContainLabelTypes.size() > 0">
            and not exists(
            select 1 from math_labels ml
            inner JOIN math_question_labels ql1 on mq.id = ql1.question_id
            where ql1.label_id = ml.id
            and ml.type in
            <foreach collection="param.notContainLabelTypes" item="labelType" open="(" separator="," close=")">
                #{labelType}
            </foreach>
            )
        </if>
        <if test="param.containLabels != null and param.containLabels.size() > 0">
            <foreach collection="param.containLabels" item="mathLabel">
                and exists (
                select mq.id from math_question_labels
                where mq.id = question_id
                and label_type = #{mathLabel.type}
                and (
                    <foreach collection="mathLabel.ids" item="labelId" separator="or">
                        label_id = #{labelId}::uuid
                    </foreach>
                    )
                )
                </foreach>
        </if>
        <if test="param.notContainDimensions != null and param.notContainDimensions.size() > 0">
            <foreach collection="param.notContainDimensions" item="dimension">
                and not exists(
                    select mq.id from math_question_dimensions
                    where mq.id = question_id
                <choose>
                    <when test="dimension.type.name() == 'PROBLEM_TEXT'">
                        and problem_text_result is not null
                    </when>
                    <when test="dimension.type.name() == 'VISUAL_ELEMENTS'">
                        and visual_elements_result is not null
                    </when>
                    <when test="dimension.type.name() == 'FORMAT_AND_TYPE'">
                        and format_and_type_result is not null
                    </when>
                    <when test="dimension.type.name() == 'CORE_KNOWLEDGE_POINTS'">
                        and core_knowledge_points_result is not null
                    </when>
                    <when test="dimension.type.name() == 'PRIMARY_SOLUTION_METHOD'">
                        and primary_solution_method_result is not null
                    </when>
                    <when test="dimension.type.name() == 'SOLUTION_LOGICAL_STRUCTURE'">
                        and solution_logical_structure_result is not null
                    </when>
                    <when test="dimension.type.name() == 'COGNITIVE_LOAD_AND_DIFFICULTY_LEVEL'">
                        and cognitive_load_difficulty_level_result is not null
                    </when>
                    <when test="dimension.type.name() == 'MATHEMATICAL_RIGOR_AND_CORRECTNESS'">
                        and mathematical_rigor_and_correctness_result is not null
                    </when>
                    <otherwise>
                        <!-- 如果没有匹配的维度类型，添加一个始终为真的条件 -->
                        and 1=1
                    </otherwise>
                </choose>
                )
            </foreach>
        </if>
        <if test="param.containDimensions != null and param.containDimensions.size() > 0">
            <foreach collection="param.containDimensions" item="dimension">
                and exists (
                    select mq.id from math_question_dimensions
                    where mq.id = question_id
                      <choose>
                          <when test="dimension.type.name() == 'PROBLEM_TEXT'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                  problem_text_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <when test="dimension.type.name() == 'VISUAL_ELEMENTS'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                      visual_elements_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <when test="dimension.type.name() == 'FORMAT_AND_TYPE'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                      format_and_type_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <when test="dimension.type.name() == 'CORE_KNOWLEDGE_POINTS'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                      core_knowledge_points_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <when test="dimension.type.name() == 'PRIMARY_SOLUTION_METHOD'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                      primary_solution_method_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <when test="dimension.type.name() == 'SOLUTION_LOGICAL_STRUCTURE'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                      solution_logical_structure_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <when test="dimension.type.name() == 'COGNITIVE_LOAD_AND_DIFFICULTY_LEVEL'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                      cognitive_load_difficulty_level_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <when test="dimension.type.name() == 'MATHEMATICAL_RIGOR_AND_CORRECTNESS'">
                              and (
                              <foreach collection="dimension.options" item="option" separator="or">
                                      mathematical_rigor_and_correctness_result = #{option.description}
                              </foreach>
                              )
                          </when>
                          <otherwise>
                              <!-- 如果没有匹配的维度类型，添加一个始终为真的条件 -->
                              and 1=1
                          </otherwise>
                      </choose>
                )
            </foreach>
        </if>
        ) a on t.id = a.id
        ORDER BY t.updated_at DESC
    </select>

    <select id="listQuestionIdsByQuestionTypes"
            resultType="com.joinus.knowledge.model.entity.QuestionTypesMapping">
        WITH
            input_qts AS (
                SELECT unnest(ARRAY[
                <foreach collection="questionTypeIds" item="questionTypeId" separator=",">
                    #{questionTypeId}::uuid  <!-- 关键点：类型转换 -->
                </foreach>
                ]::uuid[]) AS qt_id  -- 使用命名参数接收数组(示例：'{1,2,3}'::int[])
            ),
            ranked_data AS (
                SELECT
                qtm.question_type_id,
                qtm.question_id,
                CASE
                    WHEN q.review_status = 'APPROVED_SECOND_REVIEW' THEN 2
                    WHEN q.review_status = 'APPROVED_FIRST_REVIEW' THEN 1
                    ELSE 0
                    END AS sortNo,
                ROW_NUMBER() OVER (
                PARTITION BY qtm.question_type_id
                ORDER BY
                    CASE
                        WHEN q.review_status = 'APPROVED_SECOND_REVIEW' THEN 2
                        WHEN q.review_status = 'APPROVED_FIRST_REVIEW' THEN 1
                        ELSE 0
                        END DESC,
                    random()
                ) AS rk
                FROM math_question_type_questions qtm
                JOIN input_qts iq ON qtm.question_type_id = iq.qt_id
                JOIN math_questions q ON q.id = qtm.question_id and q.source = 'AI' and q.enabled = true
            )
        SELECT
            question_id,
            question_type_id,
            sortNo
        FROM ranked_data
        WHERE rk &lt;= #{questionCount}
    </select>
    <select id="queryQuestionGraphicsScriptByUser" resultType="com.joinus.knowledge.model.vo.MathQuestionGraphicsScriptVO">
        SELECT
            mq.id,
            mq.content,
            mq.question_type,
            mq.difficulty,
            mq.source,
            mq.exist_graphics,
            mq.created_at,
            mq.updated_at,
            qgs.username,
            qgs.remark,
            qgs.status,
            qgs.completed_at
        FROM
            math_questions mq
                inner join math_question_graphics_scripts qgs on mq.id = qgs.question_id
        <where>
            <if test="username == null or username == ''">
                and qgs.username is not null
            </if>
            <if test="username != null and username != ''">
                and qgs.username = #{username}
            </if>
            <if test="startDate != null">
                and qgs.completed_at &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and qgs.completed_at &lt;= #{endDate}
            </if>
            <if test="labelName != null and labelName != ''">
                and exists (
                    select 1 from math_question_labels mql
                        inner join math_labels ml on mql.label_id = ml.id
                    where mql.question_id = mq.id
                    and ml.name = #{labelName}
                )
            </if>
            <if test="questionId != null">
                and qgs.question_id = #{questionId}
            </if>
            <if test="status != null and status != ''">
                and qgs.status = #{status}
            </if>
        </where>
        order by qgs.completed_at nulls first, mq.created_at
    </select>

    <select id="listPublishInfo" resultType="com.joinus.knowledge.model.po.QuestionPublishInfoPO">
        select distinct vmkp.publisher,
               vmkp.grade,
               vmkp.semester,
               vmkp.chapter_name,
               vmkp.section_name
        from math_questions t
                 inner join math_knowledge_point_questions qkp on t.id = qkp.question_id
                 inner join math_knowledge_points mkp on qkp.knowledge_point_id = mkp.id
                 inner join view_math_knowledge_points vmkp on vmkp.knowledge_point_id = mkp.id
        where t.id = #{id}
        union
        select distinct vmqt.publisher,
                vmqt.grade,
                vmqt.semester,
                vmqt.chapter_name as chapter_name,
                vmqt.section_name as section_name
        from math_questions t
                 inner join math_question_type_questions qtm on t.id = qtm.question_id
                 inner join math_question_types mqt on qtm.question_type_id = mqt.id
                 inner join view_math_question_types  vmqt on vmqt.question_type_id = mqt.id
        where t.id = #{id}
    </select>

    <select id="listMultiKnowledgePointQuestionFromBook"
            resultType="com.joinus.knowledge.model.entity.MathQuestion">
        select mq.* from math_questions mq
        inner join math_knowledge_point_questions qkp on mq.id = qkp.question_id
        where mq.source = 'BOOK'
        and length(content) > 5
        group by mq.id
        having count(knowledge_point_id) > 1
    </select>
    <select id="listMultiQuestionTypesMappingFromBook"
            resultType="com.joinus.knowledge.model.entity.MathQuestion">
        select mq.* from math_questions mq
        inner join math_question_type_questions qtm on mq.id = qtm.question_id
        where mq.source = 'BOOK'
          and length(content) > 5
        group by mq.id
        having count(question_type_id) > 1
    </select>

    <select id="getDetailByIds" resultMap="questionDetailResultMap">
        select t.id as question_id,
               t.content as question_content,
               t.difficulty,
               t.question_type,
               t.source,
               t.enabled,
               qf.file_id as file_id,
               f.name,
               f.oss_url,
               f.oss_type,
               f.oss_bucket,
               qf.sort_no,
               qf.type,
               mqd.validation_success
        from math_questions t
                 left join math_question_files qf on t.id = qf.question_id
                 left join files f on qf.file_id = f.id
                 left join math_question_dimensions mqd on t.id = mqd.question_id
        where t.deleted_at is null
          and t.id in
          <foreach collection="ids" item="id" separator="," open="(" close=")">
              #{id}
          </foreach>
    </select>

    <select id="listQuestionAndAnswerByIds" resultMap="questionDetailWithAnswerResultMap">
        select t.id as question_id,
               t.content as question_content,
               t.difficulty,
               t.question_type,
               t.source,
               mqa.id as answer_id,
               mqa.answer,
               mqa.content as answer_content,
               f.id as file_id,
               f.oss_url
        from math_questions t
                 left join math_question_answers qar on t.id = qar.question_id
                 left join math_answers mqa on qar.answer_id = mqa.id and mqa.deleted_at is null
                 left join math_question_files qf on t.id = qf.question_id
                 left join files f on qf.file_id = f.id and f.deleted_at is null
        where t.deleted_at is null
          and t.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        order by t.updated_at asc
    </select>
    <select id="listNotEnoughAIGeneratedQuestionCount"
            resultType="com.joinus.knowledge.model.po.AIGeneratedQuestionCount">
        SELECT
        mq.id as question_id,
        COALESCE(t_total.total_count, 0) AS total_count,
        COALESCE(t_total.total_count, 0) AS enabled_count
        FROM
        math_questions mq
        LEFT JOIN
        (SELECT
        base_question_id,
        COUNT(base_question_id) AS total_count
        FROM
        math_question_relationships
        GROUP BY
        base_question_id) AS t_total
        ON
        mq.id = t_total.base_question_id
        WHERE
        mq.source = 'BOOK'
        AND mq.deleted_at is null
        AND length(mq.content) > 5
        AND COALESCE(t_total.total_count, 0) &lt; #{count}
    </select>
    <select id="listConditionalNotEnoughAIGeneratedQuestionCount"
            resultType="com.joinus.knowledge.model.po.AIGeneratedQuestionCount">
        SELECT
        mq.id as question_id,
        COALESCE(t_total.total_count, 0) AS total_count,
        COALESCE(t_conditional.enabled_count, 0) AS enabled_count
        FROM
        math_questions mq
        LEFT JOIN
        (SELECT
        base_question_id,
        COUNT(base_question_id) AS total_count
        FROM
        math_question_relationships
        GROUP BY
        base_question_id) AS t_total
        ON
        mq.id = t_total.base_question_id
        LEFT JOIN
        (SELECT
        mqr.base_question_id,
        COUNT(mqr.base_question_id) AS enabled_count
        FROM
        math_question_relationships AS mqr
        INNER JOIN
        math_questions AS dmq ON mqr.derived_question_id = dmq.id AND dmq.enabled = true
        GROUP BY
        mqr.base_question_id) AS t_conditional
        ON
        mq.id = t_conditional.base_question_id
        WHERE
        mq.source = 'BOOK'
        AND mq.deleted_at is null
        AND length(mq.content) > 5
        AND COALESCE(t_conditional.enabled_count, 0) &lt; 10
        AND COALESCE(t_total.total_count, 0) &lt; 50
    </select>

    <select id="listPastExamPaperQuestions"
            resultType="com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO">
        WITH cte AS (
            select qkp.*,
                me.year as pastExamPaperYear,
                me.region as pastExamPaperRegion,
                ROW_NUMBER() OVER (PARTITION BY qkp.knowledge_point_id ORDER BY me.year DESC) AS rn
            from math_questions t
            inner join math_exam_questions meq on t.id = meq.question_id
            inner join math_exams me on meq.exam_id = me.id and me.deleted_at is null and me.source = '中考真题'
            inner join math_knowledge_point_questions qkp on t.id = qkp.question_id
            where t.deleted_at is null
                and t.source = #{source.name}
                and qkp.knowledge_point_id in
                <foreach item="item" collection="knowledgePointIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
        )
        SELECT *
        FROM cte
        WHERE rn &lt;= #{questionCount}
    </select>

    <select id="listExamQuestionDetailByExamId" resultType="com.joinus.knowledge.model.vo.QuestionDetailVO">
        select mq.*,
               meq.sort_no,
               me.year,
               me.region
        from math_questions mq
                 inner join math_exam_questions meq on meq.question_id = mq.id
                 inner join math_exams me on meq.exam_id = me.id
        where meq.exam_id = #{examId}
        order by mq.created_at
    </select>
    <select id="listNoneKnowledgePointsExamQuestions" resultType="com.joinus.knowledge.model.entity.MathQuestion">
        select mq.* from math_questions mq
        where source in ('ZHONG_KAO_EXAM', 'REGULAR_EXAM')
        and not exists(select 1 from math_knowledge_point_questions mkpq
                        where mkpq.question_id = mq.id)
        order by mq.created_at
        limit 10
    </select>

    <select id="getExamInfo" resultType="com.joinus.knowledge.model.vo.QuestionDetailVO">
        select t.*,
               meq.sort_no as sortNoInExam,
               me.id   as exam_id,
               me.name as exam_name,
               me.region as pastExamPaperRegion,
               me.year as pastExamPaperYear
        from math_questions t
                 left join math_exam_questions meq on t.id = meq.question_id
                 left join math_exams me on meq.exam_id = me.id and me.deleted_at is null
        where t.deleted_at is null
          and t.id = #{id}
    </select>
    <select id="listAvailableAIQuestionsByKnowledgePointId" resultType="com.joinus.knowledge.model.entity.MathQuestion">
        select mq.* from math_questions mq
        inner join math_knowledge_point_questions mkpq on mq.id = mkpq.question_id
        where mq.enabled = true
          and mq.source = 'AI'
          and mkpq.knowledge_point_id = #{knowledgePointId}
    </select>
    <select id="listAvailableAIQuestionsByQuestionTypeId" resultType="com.joinus.knowledge.model.entity.MathQuestion">
        select mq.* from math_questions mq
        inner join math_question_type_questions mqtq on mq.id = mqtq.question_id
        where mq.enabled = true
          and mq.source = 'AI'
          and mqtq.question_type_id = #{questionTypeId}
    </select>

    <select id="listMathHolidayTrainingQuestions"
            resultType="com.joinus.knowledge.model.po.TrainingQuestionPO">
        select t.section_id     as sectionId,
               mkp.id           as knowledgePointId,
               null             as questionTypeId,
               mq.id            as questionId,
               mq.question_type as questionType,
               t.page_index     as pageIndex,
               vmkp.section_sort_no,
               vmkp.publisher as publisher,
               mq.review_status
        from math_section_knowledge_points t
                 inner join math_knowledge_points mkp on t.knowledge_point_id = mkp.id and mkp.deleted_at IS NULL
                 inner join math_knowledge_point_questions mkpq on mkp.id = mkpq.knowledge_point_id
                 inner join math_questions mq on mkpq.question_id = mq.id and mq.deleted_at IS NULL
                 inner join view_math_knowledge_points vmkp on t.section_id = vmkp.section_id
        where mq.source = 'AI'
          and mq.enabled = true
          <if test="null != sectionId">
              and t.section_id = #{sectionId}
          </if>
          <if test="null != chapterId">
              and ms.chapter_id = #{chapterId}
          </if>
        union
        select t.section_id     as sectionId,
               null             as knowledgePointId,
               mqt.id           as questionTypeId,
               mq.id            as questionId,
               mq.question_type as questionType,
               t.page_index     as pageIndex,
               vmqt.section_sort_no,
               vmqt.publisher as publisher,
               mq.review_status
        from math_section_question_types t
                 inner join math_question_types mqt on t.question_type_id = mqt.id and mqt.deleted_at IS NULL
                 inner join math_question_type_questions mqtq on mqt.id = mqtq.question_type_id
                 inner join math_questions mq on mqtq.question_id = mq.id and mq.deleted_at IS NULL
                 inner join view_math_question_types vmqt on t.section_id = vmqt.section_id
        where mq.source = 'AI'
          and mq.enabled = true
            and exists (
            select 1 from math_knowledge_point_question_types mkpqt where mkpqt.question_type_id = mqt.id
            )
          <if test="null != sectionId">
              and t.section_id = #{sectionId}
          </if>
          <if test="null != chapterId">
              and ms.chapter_id = #{chapterId}
          </if>
    </select>


</mapper>
